<!DOCTYPE html>
<html>
<head>
    <base href="$FLUTTER_BASE_HREF">
    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="Dubai Page">

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Dubai Page">
    <link rel="apple-touch-icon" href="favicon.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png">

    <title>Aqar Dubai</title>
    <link rel="manifest" href="manifest.json">

    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
          name="viewport">

    <script>
        // The value below is injected by flutter build, do not touch.
        const serviceWorkerVersion = null;
    </script>

    <!-- Flutter initialization -->
    <script src="flutter.js" defer></script>
</head>
<body style="margin:0; background-color: transparent;">
<script>
    window.addEventListener('load', function() {
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
</script>

<script type="text/javascript">
    window.flutterWebRenderer = "html";
</script>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAcl3LOVhYIjlWPWjuONGesjhEeDzRPmlY"></script>
</body>
</html>